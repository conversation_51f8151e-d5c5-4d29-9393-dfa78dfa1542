import Vue from 'vue'
import VueRouter from 'vue-router'
import config from '@/config'
import store from '../store'

// Variable to hold the previous path
let previousPath = ''

Vue.use(VueRouter)
const collaborator = JSON.parse(localStorage.getItem('collaborator'))
const tenantType = JSON.parse(localStorage.getItem(config.localstorageKeys.TENANT_TYPE))
const routes = [
  {
    path: '',
    name: 'LandingPage',
    component: () => import('@/views'),
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/DASHBOARD'),
        beforeEnter: (to, from, next) => {
          if (localStorage.getItem(config.localstorageKeys.AUTH)) {
            next()
          } else {
            next('/login')
          }
        }
      },
      {
        path: '/invite-tenant',
        name: 'InviteTenant',
        component: () => import('@/views/MANAGE/inviteTenant'),
        beforeEnter: (to, from, next) => {
          if (tenantType === 2) {
            next('')
          } else {
            next()
          }
        }
      },
      {
        path: '/invite-user',
        name: 'InviteTenant',
        component: () => import('@/views/MANAGE/inviteUser')
      },
      {
        path: '/edit-user/:tenantId',
        name: 'EditUser',
        component: () => import('@/components/manage/editInvitedUser.vue')
      },
      {
        path: '/materialmaster',
        name: 'Material-Master',
        component: () => import('@/views/MATERIAL-MASTER/copyIndex.vue'),
        beforeEnter: (to, from, next) => {
          if (tenantType === 2) {
            next('')
          } else {
            next()
          }
        }
      },
      {
        path: '/copy-materialmaster',
        name: 'Material-Master',
        component: () => import('@/views/MATERIAL-MASTER/copyIndex.vue'),
        beforeEnter: (to, from, next) => {
          if (tenantType === 2) {
            next('')
          } else {
            next()
          }
        }
      },
      {
        path: '/materialmaster/:pageNumber',
        name: 'Material-Master',
        component: () => import('@/views/MATERIAL-MASTER/copyIndex.vue'),
        children: [
          {
            path: ':materialId',
            name: 'MaterialData',
            component: () => import('@/views/MATERIAL-MASTER/copyIndex.vue')
          }
        ],
        beforeEnter: (to, from, next) => {
          if (tenantType !== 2 || collaborator) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/copy-materialmaster/:pageNumber',
        name: 'Material-Master',
        component: () => import('@/views/MATERIAL-MASTER/copyIndex.vue'),
        children: [
          {
            path: ':materialId',
            name: 'MaterialData',
            component: () => import('@/views/MATERIAL-MASTER/copyIndex.vue')
          }
        ],
        beforeEnter: (to, from, next) => {
          if (tenantType !== 2 || collaborator) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/profile',
        name: 'Profile',
        component: () => import('@/views/USER/Profile.vue')
      },
      {
        path: '/form',
        name: 'Form',
        component: () => import('@/views/FORM/copyIndex.vue'),
        children: [
          {
            path: ':templateId/:templateName',
            name: 'FormList',
            component: () => import('@/views/FORM/copyFormList.vue')
          }
        ],
        beforeEnter: (to, from, next) => {
          if (tenantType !== 2 || collaborator) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/copy-form',
        name: 'Form',
        component: () => import('@/views/FORM/copyIndex.vue'),
        children: [
          {
            path: ':templateId/:templateName',
            name: 'FormList',
            component: () => import('@/views/FORM/copyFormList.vue')
          }
        ],
        beforeEnter: (to, from, next) => {
          if (tenantType !== 2 || collaborator) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: 'form/createform/:templateId/:templateName',
        name: 'FormList',
        component: () => import('@/views/FORM/createForm.vue')
      },
      {
        path: 'form/createform/:templateId/:templateName/:materialId/:type/:page',
        name: 'FormList',
        component: () => import('@/views/FORM/createForm.vue')
      },
      {
        path: 'form/createform/:templateId/:templateName/:parentFormId',
        name: 'FormList',
        component: () => import('@/views/FORM/createForm.vue')
      },
      {
        path: 'form/viewform/:templateId/:templateName/:formId',
        name: 'ViewForm',
        component: () => import('@/views/FORM/viewForm.vue')
      },
      {
        path: 'form/editform/:templateId/:templateName/:formId',
        name: 'EditForm',
        component: () => import('@/views/FORM/editForm.vue')
      },
      {
        path: 'form/editform/:templateId/:templateName/:formId/:materialId',
        name: 'EditForm',
        component: () => import('@/views/FORM/editForm.vue')
      },
      {
        path: '/bom/product',
        name: 'ProductBomList',
        component: () => import('@/views/BOM/product'),
        children: [
          {
            path: '',
            name: 'NoProductSelected',
            component: () => import('@/views/BOM/product/noProductSelected.vue')
          },
          {
            path: ':productCode/bom/new',
            name: 'NewBom',
            component: () => import('@/views/BOM/product/createBom.vue')
          },
          {
            path: ':productCode/bom/:bomId/edit',
            name: 'EditBom',
            component: () => import('@/views/BOM/product/editBom.vue')
          },
          {
            path: ':productCode',
            name: 'ProductBomList',
            component: () => import('@/views/BOM/product/bomList.vue'),
            children: [
              {
                path: 'bom/:bomId',
                name: 'BomDetail',
                component: () => import('@/views/BOM/product/copyBomDetail.vue')
              },
              {
                path: 'copy-bom/:bomId',
                name: 'BomDetail',
                component: () => import('@/views/BOM/product/copyBomDetail.vue')
              }
            ]
          }
        ]
      },
      {
        path: '/bom/project',
        name: 'ProjectBomList',
        component: () => import('@/views/BOM/project'),
        beforeEnter: (to, from, next) => {
          if (collaborator || tenantType !== 2) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        },
        children: [
          {
            path: 'bom/new',
            name: 'NewProjectBom',
            component: () => import('@/views/BOM/project/createBom.vue')
          },
          {
            path: '',
            name: 'ProjectBomList',
            component: () => import('@/views/BOM/project/bomList.vue'),
            children: [
              {
                path: 'bom/:bomId',
                name: 'ProjectBomDetail',
                component: () => import('@/views/BOM/project/copyBomDetail.vue')
              },
              {
                path: 'copy-bom/:bomId',
                name: 'ProjectBomDetail',
                component: () => import('@/views/BOM/project/copyBomDetail.vue')
              }
            ]
          },
          {
            path: 'bom/:bomId/edit',
            name: 'EditProjectBom',
            component: () => import('@/views/BOM/project/editBom.vue')
          }
        ]
      },
      {
        path: '/bom/compare/project',
        name: 'CompareProjectBom',
        component: () => import('@/views/BOM/compare/compareProjectBom.vue')
      },
      {
        path: '/bom/compare/product/:productCode',
        name: 'CompareProductBom',
        component: () => import('@/views/BOM/compare/compareProductBom.vue')
      },
      {
        path: '/bom/compare/:bomId',
        name: 'CompareBom',
        component: () => import('@/views/BOM/compare/compareBomVersion.vue')
      },
      {
        path: '/document-view',
        name: 'Document-View',
        beforeEnter: (to, from, next) => {
          if (tenantType === 2 && !collaborator) {
            next('')
          } else {
            next()
          }
        },
        component: () => import('@/views/DOCUMENT-VIEW/index.vue')
      },
      {
        path: '/document-view/:documentId',
        name: 'Document-View',
        component: () => import('@/views/DOCUMENT-VIEW/index.vue')
      },
      {
        path: '/project',
        name: 'Project',
        component: () => import('@/views/PROJECT/copyIndex.vue'),
        beforeEnter: (to, from, next) => {
          if (!collaborator && tenantType !== 2) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/copy-project',
        name: 'Project',
        component: () => import('@/views/PROJECT/copyIndex.vue'),
        beforeEnter: (to, from, next) => {
          if (!collaborator && tenantType !== 2) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/copy-projects',
        name: 'Copy-Project',
        component: () => import('@/views/PROJECT/copyProject.vue'),
        beforeEnter: (to, from, next) => {
          if (!collaborator && tenantType !== 2) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/projects',
        name: 'Projects',
        component: () => import('@/views/PROJECT/copyProject.vue'),
        beforeEnter: (to, from, next) => {
          if (!collaborator && tenantType !== 2) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/insights',
        name: 'Insights',
        component: () => import('@/views/INSIGHTS/index.vue'),
        beforeEnter: (to, from, next) => {
          if (tenantType === 2 && !collaborator) {
            next('')
          } else {
            next()
          }
        },
        children: [
          {
            path: 'product',
            name: 'Product',
            component: () => import('@/views/INSIGHTS/product.vue')
          },
          {
            path: 'product/personal',
            name: 'ProductPersonal',
            component: () => import('@/views/INSIGHTS/copyProductPersonal.vue')
          },
          {
            path: 'product/copy-personal',
            name: 'ProductPersonal',
            component: () => import('@/views/INSIGHTS/copyProductPersonal.vue')
          },
          {
            path: 'project',
            name: 'Project',
            component: () => import('@/views/INSIGHTS/project/project.vue')
          },
          {
            path: 'project/personal',
            name: 'ProjectPersonal',
            component: () => import('@/views/INSIGHTS/project/copyProjectPersonal.vue')
          },
          {
            path: 'project/copy-personal',
            name: 'ProjectPersonal',
            component: () => import('@/views/INSIGHTS/project/copyProjectPersonal.vue')
          }

        ]
      },
      {
        path: '/project-planner',
        name: 'ProjectPlanner',
        beforeEnter: (to, from, next) => {
          if (tenantType === 2 && !collaborator) {
            next('')
          } else {
            next()
          }
        },
        component: () => import('@/views/PROJECT-PLANNER'),
        children: [
          {
            path: ':task_id',
            name: 'Specific Task',
            component: () => import('@/views/PROJECT-PLANNER')
          }
        ]
      },
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/SETTINGS'),
        children: [
          {
            path: 'custom-list',
            name: 'Custom List',
            component: () => import('@/views/SETTINGS/customList.vue')
          },
          {
            path: 'tenant-calendar',
            name: 'Tenant Calendar',
            component: () => import('@/views/SETTINGS/tenant-calendar.vue')
          },
          {
            path: 'copy-custom-list',
            name: 'Custom List',
            component: () => import('@/views/SETTINGS/customList.vue')
          },
          {
            path: 'tags',
            name: 'Tags',
            component: () => import('@/views/SETTINGS/tags.vue')
          },
          {
            path: 'calendar',
            name: 'Calendar',
            component: () => import('@/views/SETTINGS/calendar.vue')
          },
          {
            path: 'forms',
            name: 'Forms',
            component: () => import('@/views/SETTINGS/forms.vue')
          },
          {
            path: 'copy-forms',
            name: 'Forms',
            component: () => import('@/views/SETTINGS/copyForms.vue')
          },
          {
            path: 'storage-locations',
            name: 'Storage Locations',
            component: () => import('@/views/SETTINGS/storageLocation.vue')
          },
          {
            path: 'copy-storage-locations',
            name: 'Storage Locations',
            component: () => import('@/views/SETTINGS/copyStorageLocation.vue')
          },
          {
            path: 'user-groups',
            name: 'User Groups',
            component: () => import('@/views/SETTINGS/copyUserGroups.vue')
          },
          {
            path: 'copy-user-groups',
            name: 'User Groups',
            component: () => import('@/views/SETTINGS/copyUserGroups.vue')
          },
          {
            path: 'user-groups/create',
            name: 'Create User Group',
            component: () => import('@/views/SETTINGS/addUserGroup.vue')
          },
          {
            path: 'user-groups/edit/:id',
            name: 'Edit User Group',
            component: () => import('@/views/SETTINGS/editUserGroup.vue')
          },
          {
            path: 'user-groups/detail/:id',
            name: 'User Group Detail',
            component: () => import('@/views/SETTINGS/userGroupDetail.vue')
          },
          {
            path: 'workflows/:pageNo',
            name: 'Workflow',
            component: () => import('@/views/SETTINGS/workflow.vue')
          },
          {
            path: 'copy-workflows/:pageNo',
            name: 'Workflow',
            component: () => import('@/views/SETTINGS/copyWorkFlow.vue')
          },
          {
            path: 'sequence-generator/create',
            name: 'Workflow',
            component: () => import('@/views/PART-ID-GENERATION/index.vue')
          },
          {
            path: 'sequence-generator/view/:id',
            name: 'Workflow',
            component: () => import('@/views/PART-ID-GENERATION/index.vue')
          },
          {
            path: 'sequence-generator',
            name: 'Workflow',
            component: () => import('@/views/PART-ID-GENERATION/IDGeneration.vue'),
            children: [
              {
                path: 'create',
                name: 'Create Part Id',
                component: () => import('@/views/PART-ID-GENERATION/index.vue')
              }
            ]
          },
          {
            path: '/workflows/:type/:templateId',
            name: 'Workflows',
            component: () => import('@/views/WORKFLOWS/index.vue')
          },
          {
            path: 'config-feature',
            name: 'Config Feature',
            component: () => import('@/views/SETTINGS/configFeature.vue')
          }
        ],
        beforeEnter: (to, from, next) => {
          if (!collaborator && tenantType !== 2) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/timesheet',
        name: 'TimeSheet',
        beforeEnter: (to, from, next) => {
          if (tenantType === 2) {
            next('')
          } else {
            next()
          }
        },
        component: () => import('@/views/TIME-SHEET'),
        children: [
          {
            path: 'update',
            name: 'Update Timesheet',
            component: () => import('@/views/TIME-SHEET/copyUpdate.vue')
          },
          {
            path: 'copy-update',
            name: 'Copy Update Timesheet',
            component: () => import('@/views/TIME-SHEET/copyUpdate.vue')
          },
          {
            path: 'reports',
            name: 'TimeSheetReports',
            component: () => import('@/views/TIME-SHEET/reports.vue')
          },
          {
            path: 'teamWise',
            name: 'projectWiseView', // this is for project wise view for admins
            component: () => import('@/views/TIME-SHEET/teamWise.vue')
          }
        ]
      },
      {
        path: '/trendsandforecast',
        name: 'Forecast',
        component: () => import('@/views/FORECAST'),
        children: [
          {
            path: 'material-forecast',
            name: 'Material Forecast',
            component: () => import('@/views/FORECAST/copyMaterialForcast.vue')
          },
          {
            path: 'resource-trends',
            name: 'Materail Forecast',
            component: () => import('@/views/FORECAST/resourceTrends.vue')
          }
        ],
        beforeEnter: (to, from, next) => {
          if (!collaborator && tenantType !== 2) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/trendsandforecast',
        name: 'Forecast',
        component: () => import('@/views/FORECAST'),
        children: [
          {
            path: 'copy-material-forecast',
            name: 'Material Forecast',
            component: () => import('@/views/FORECAST/copyMaterialForcast.vue')
          },
          {
            path: 'copy-resource-trends',
            name: 'Materail Forecast',
            component: () => import('@/views/FORECAST/copyResourceTrends.vue')
          }
        ],
        beforeEnter: (to, from, next) => {
          if (!collaborator && tenantType !== 2) {
            next() // Allow navigation
          } else {
            // Redirect to a different page or display an error message
            next('')
          }
        }
      },
      {
        path: '/form-builder',
        name: 'FormBuilder',
        component: () => import('@/views/SETTINGS/formBuilder.vue')
      },
      {
        path: '/copy-form-builder',
        name: 'FormBuilder',
        component: () => import('@/views/SETTINGS/formBuilder2.vue')
      },
      {
        path: '/copy-form-editor/:id',
        name: 'EditFormBuilder',
        component: () => import('@/views/SETTINGS/formBuilder2.vue')
      },
      {
        path: '/form-editor/:formId',
        name: 'FormEditor',
        component: () => import('@/views/SETTINGS/formEditor.vue')
      },
      {
        path: '/beacon-dashboard',
        name: 'Beacon Dashboard',
        component: () => import('@/views/BEACON-DASHBOARD')
      },
      {
        path: '/workflow-dashboard',
        name: 'Workflow Dashboard',
        component: () => import('@/views/WORKFLOW-DASHBOARD')
      },
      {
        path: 'workflows/:type',
        name: 'Workflows',
        component: () => import('@/views/WORKFLOWS/index.vue')
      }
    ]
  },

  {
    path: '/login',
    name: 'LoginPage',
    component: () => import('@/views/MANAGE/login.vue')
  },
  {
    path: '/signup',
    name: 'SignupPage',
    component: () => import('@/views/MANAGE/signup.vue')
  },
  {
    path: '/forgot-password',
    name: 'ForgotPasswordPage',
    component: () => import('@/views/MANAGE/forgotPassword.vue')
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// Global beforeEach guard
router.beforeEach((to, from, next) => {
  // Update previousPath before navigating
  previousPath = from.path
  if (['/login', '/signup', 'forgot-password'].includes(to.path) && store.getters?.openTenantId) {
    next('/')
  } else {
    next()
  }
})

export { router, previousPath }
