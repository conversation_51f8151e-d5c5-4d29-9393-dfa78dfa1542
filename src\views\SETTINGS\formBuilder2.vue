<template>
  <div class="form-builder">
    <div class="header-container">
      <div class="header-content">
        <img
          src="~@/assets/images/icons/arrow-back.svg"
          alt="Back"
          class="back-button pointer"
          @click="goToPrevious"
        />

        <div class="stepper-header-container">
          <StepperHeader
            :step="activeStep"
            @step-click="handleStepChange"
          />
        </div>

        <!-- Empty div for flex balance -->
        <div class="header-spacer"></div>
      </div>
    </div>

    <div class="header-divider"></div>

    <!-- Smooth transitions between steps -->
    <transition name="slide-fade" mode="out-in">
      <div
        v-if="activeStep === 'Basic Info'"
        key="basic-info"
        class="content-container centered full-width"
        style="padding-top: 20px;"
      >
        <FormTemplateSetup
          :isEdit="isEdit"
          :editData="formData"
          :workflows="workflowTempaltes"
          :sequenceTemplates="formSequenceTemplates"
          :filteredFormTypeList="filteredFormTypeList"
          @create-template="handleCreateTemplate"
          @change-form-type="changeFormTypeInFormData"
        />
      </div>

      <div
        v-else-if="activeStep === 'Customise Fields'"
        key="customise-fields"
        class="content-container full-width"
      >
        <FormCanvas
          :isEdit="isEdit"
          :formData="formData"
          :fixedFields="fixedFields"
          :customFields="customFields"
          @add-custom-field="customFields.push($event)"
          @save-template="handleSaveTemplate"
          @reorder-fixed-fields="handleReorderFixedFields"
        />
      </div>

      <div
        v-else-if="activeStep === 'Completed'"
        key="completed"
        class="content-container centered"
      >
        <div class="success-screen">
          <div class="check-icon">
            <img src="~@/assets/images/circle-check.svg" style="width: 80px; height: 80px;" alt="Success" />
          </div>
          <div class="success-message">
            <h2 v-if="isEdit">Template Updated Successfully!</h2>
            <h2 v-else>Template Created Successfully!</h2>
            <p>Your form template "<strong>{{ formData.templateName }}</strong>" has been saved.</p>
            <p class="sub">Redirecting to template list...</p>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import StepperHeader from '@/components/form/stepsHeader.vue'
import FormTemplateSetup from '@/components/form/formTemplateSetup.vue'
import FormCanvas from '@/components/form/formCanvas.vue'
import { mapGetters } from 'vuex'
import { getFullWorkFLowTemplates, GetTemplateData, getFormTypeList, createForm, getDetailFormTemplate, updateForm } from '@/api'
import { alert } from '@/plugins/notification'
import config from '@/config'

export default {
  components: {
    FormTemplateSetup,
    StepperHeader,
    FormCanvas
  },
  data () {
    return {
      id: null,
      editInfo: null,
      isEdit: false,
      config: config,
      formTypeList: [],
      formSequenceTemplates: [],
      workflowTempaltes: [],
      WFTLoading: false,
      formData: {
        formTypeName: '',
        templateName: '',
        formType: '',
        selectedWorkflow: {},
        sequence_template_id: null
      },
      activeStep: 'Basic Info',
      fixedFields: [],
      customFields: [],
      step: 'Upcoming',
      templateCreated: false,
      formType: null,
      templateName: '',
      showAddFieldModal: false
    }
  },
  computed: {
    ...mapGetters('form', [
      'formFields',
      'formTemplateBody',
      'selectedFormElementToEdit',
      'preventElementSelection',
      'formTemplateName',
      'formTemplateType',
      'linkedWFTemplate',
      'linkedFormSequenceId'
    ]),
    filteredFormTypeList () {
      return this.formTypeList.filter(type =>
        type.id !== this.config.STANDARD_MATERIAL_FORM.form_type &&
        type.id !== this.config.STANDARD_BOM_FORM.form_type
      )
    }
  },
  mounted () {
    getFormTypeList().then(res => {
      this.formTypeList = res?.core_form_types
    })
  },
  methods: {
    goToPrevious () {
      if (this.activeStep === 'Customise Fields') {
        this.activeStep = 'Basic Info'
      } else if (this.activeStep === 'Basic Info') {
        this.$router.push('/settings/copy-forms')
      }
    },
    handleSaveTemplate (fields) {
      if (!this.isEdit) {
        createForm(fields, this.formData.formType, this.formData.templateName, this.formData.workflowId, this.formData.sequence_template_id)
          .then(res => {
            if (res) {
              this.activeStep = 'Completed'
              setTimeout(() => {
                this.$router.push('/settings/copy-forms')
              }, 2000)
            }
          })
          .catch(err => {
            alert('Something went wrong')
            console.error('Error while creating form template:', err)
          })
      } else {
        updateForm(this.id, fields, this.formData.templateName, this.formData.workflowId, this.formData.sequence_template_id)
          .then(res => {
            if (res) {
              this.activeStep = 'Completed'
              setTimeout(() => {
                this.$router.push('/settings/copy-forms')
              }, 2000)
            }
          })
          .catch(err => {
            alert('Something went wrong')
            console.error('Error while creating form template:', err)
          })
      }
    },
    handleReorderFixedFields (event) {
      const { fromIndex, toIndex } = event
      const newFixedFields = [...this.fixedFields]
      const draggedField = newFixedFields.splice(fromIndex, 1)[0]
      newFixedFields.splice(toIndex, 0, draggedField)
      this.fixedFields = newFixedFields
    },
    changeFormTypeInFormData (event) {
      this.formData.formType = event.target.value
      this.changeFormType()
    },
    async getTemplatesForWFL () {
      try {
        this.WFTLoading = true
        this.workflowTempaltes = []
        const res = await getFullWorkFLowTemplates(config.CORE_FEATURES.FORMS)
        this.workflowTempaltes = res.workflow_templates
        if (res) {
          GetTemplateData().then(res => {
            const templates = res?.core_sequence_id_template || []

            this.formSequenceTemplates = templates.filter(
              template => template.core_feature?.id === 5
            )
          })
        }
        this.WFTLoading = false
      } catch (error) {
        alert('Error fetching  form templates')
        this.WFTLoading = false
        console.error('Error fetching templates', error)
      }
    },
    changeFormType () {
      const formDefaultFields = (
        this.formTypeList.find((type) => type.id === this.formData.formType)
          .default_fields || []
      ).map((type) => {
        const key = this.formFields.find(
          (field) => field.id === type.field_type_id
        ).key
        return {
          ...type,
          key
        }
      })
      this.$store.commit(
        'form/setFormTemplateType',
        this.formData.formType
      )
      this.$store.dispatch('form/setSelectedFormElementToEdit', null)
      this.$store.dispatch('form/removeTemplateElementFormTemplateBody')
      this.$store.dispatch(
        'form/addTemplateElementToTemplateBody',
        formDefaultFields
      )
    },
    handleStepChange (step) {
      this.activeStep = step
    },
    addCustomField (newField) {
      this.customFields.push(newField)
    },
    handleCreateTemplate ({ formType, templateName, workflowId, sequenceType, formTypeName }) {
      this.templateCreated = true
      this.formData.formType = formType
      this.formData.templateName = templateName
      this.formData.workflowId = workflowId
      this.formData.formTypeName = formTypeName
      this.formData.sequence_template_id = sequenceType
      this.activeStep = 'Customise Fields'
      this.fixedFields = this.getPredefinedFields(formType)
    },
    getPredefinedFields (typeId) {
      const selectedType = this.formTypeList.find(t => t.id === typeId)
      if (!selectedType) return []

      if (this.isEdit) {
        return selectedType.default_fields
      } else {
        return selectedType.default_fields.filter(field => field.autogenerated === false)
      }
    }
  },
  created () {
    const id = this.$route.params.id
    if (id) {
      this.id = id
      this.isEdit = true
      getDetailFormTemplate(id).then(res => {
        const values = res.core_form_templates_by_pk
        // Handle both fixed and custom fields for edit mode
        const allTemplateFields = values.template_versions[0].template_fields || []

        this.customFields = allTemplateFields
          .filter(field => field.fixed === false)
          .map(field => ({
            ...field,
            name: field.field_name
          }))

        this.fixedFields = allTemplateFields
          .filter(field => field.fixed === true)
          .map(field => ({
            ...field,
            name: field.field_name
          }))

        this.formData.templateName = values?.name
        this.formData.formType = values.core_form_type.id
        this.formData.sequence_template_id = values?.core_sequence_id_template?.id
        this.formData.workflowId = values?.workflow_template_id
      })
    }
    this.getTemplatesForWFL()
  }
}
</script>

<style scoped>
.form-builder {
  height: 86vh;
  width: 100%;
  display: flex;
  gap: 0px;
  flex-direction: column;
  overflow: hidden;
}

.header-container {
  width: 100%;
  padding: 0 20px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 0;
  margin-bottom: 20px;
}

.back-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  flex-shrink: 0;
}

.stepper-header-container {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-spacer {
  width: 24px;
  flex-shrink: 0;
}

.header-divider {
  height: 1px;
  background-color: #e0e0e0;
  flex-shrink: 0;
}

.content-container {
  flex: 1;
  width: 100%;
  height: 70%;
  overflow: hidden;
  position: relative;
}

.content-container.centered {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.content-container.full-width {
  /* display: flex; */
  width: 100%;
}

.success-screen {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  width: 100%;
  padding: 40px 20px;
}

.check-icon {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.check-icon img {
  width: 64px !important;
  height: 64px !important;
  background-color: #10b981;
  border-radius: 50%;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.success-message {
  max-width: 400px;
}

.success-message h2 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #111827;
  line-height: 1.2;
}

.success-message p {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 8px;
  line-height: 1.4;
}

.success-message p strong {
  color: #374151;
  font-weight: 500;
}

.success-message .sub {
  font-size: 0.875rem;
  color: #9ca3af;
  margin-top: 16px;
  margin-bottom: 0;
}

/* Smooth slide transition */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.5s ease-in-out;
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(50px);
}

.slide-fade-enter-to {
  opacity: 1;
  transform: translateX(0);
}

.slide-fade-leave-from {
  opacity: 1;
  transform: translateX(0);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-50px);
}

/* Old fade-slide transition (keeping for backward compatibility) */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 1.5s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.fade-slide-enter-to {
  opacity: 1;
  transform: translateY(0);
}

.fade-slide-leave-from {
  opacity: 1;
  transform: translateY(0);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
