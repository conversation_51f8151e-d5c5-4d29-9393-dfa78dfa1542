import { envConfig } from './env.config'
const location = window.location.hostname
let GRAPHQL_ENDPOINT = envConfig.local.GRAPHQL_ENDPOINT
let SERVER_ENDPOINT = envConfig.local.SERVER_ENDPOINT
let GRAPHQL_WS_ENDPOINT = envConfig.local.GRAPHQL_WS_ENDPOINT
if (location === 'localhost') {
  GRAPHQL_ENDPOINT = envConfig.local.GRAPHQL_ENDPOINT
  SERVER_ENDPOINT = envConfig.local.SERVER_ENDPOINT
  GRAPHQL_WS_ENDPOINT = envConfig.local.GRAPHQL_WS_ENDPOINT
} else if (location === 'dev.beacon-dtx.com') {
  GRAPHQL_ENDPOINT = envConfig.dev.GRAPHQL_ENDPOINT
  SERVER_ENDPOINT = envConfig.dev.SERVER_ENDPOINT
  GRAPHQL_WS_ENDPOINT = envConfig.dev.GRAPHQL_WS_ENDPOINT
} else if (location === 'stg.beacon-dtx.com') {
  GRAPHQL_ENDPOINT = envConfig.stg.GRAPHQL_ENDPOINT
  SERVER_ENDPOINT = envConfig.stg.SERVER_ENDPOINT
  GRAPHQL_WS_ENDPOINT = envConfig.stg.GRAPHQL_WS_ENDPOINT
} else if (location === 'beacon-dtx.com') {
  GRAPHQL_ENDPOINT = envConfig.prod.GRAPHQL_ENDPOINT
  SERVER_ENDPOINT = envConfig.prod.SERVER_ENDPOINT
  GRAPHQL_WS_ENDPOINT = envConfig.prod.GRAPHQL_WS_ENDPOINT
}
export default {
  graphQLEndpoint: GRAPHQL_ENDPOINT,
  serverEndpoint: SERVER_ENDPOINT,
  graphQLWsEndpoint: GRAPHQL_WS_ENDPOINT,
  landingPage: {},
  localstorageKeys: {
    AUTH: 'dtxtoken',
    TENANT: 'dtxTenantToken',
    PROJECT: 'dtxProjectToken',
    LAST_OPENED_PROJECT: 'projectId',
    LAST_OPENED_TENANT: 'tenantId',
    PROJECT_TOKEN_NAME: 'projectTokenMap',
    COLLABORATOR: 'collaborator',
    COLLABORATOR_ID: 'collaboratorId',
    TENANT_TYPE: 'tenantType',
    REDIRECT_FROM: 'redirectFrom',
    TARGET_TENANT_ID: 'targetTenantId',
    LAST_PATH: 'lastPath'
  },
  userRole: {
    1: 'Admin',
    2: 'Editor',
    3: 'Collaborator',
    4: 'Viewer'
  },
  USER_STATUS_MAP: {
    ACTIVE: 1,
    INACTIVE: 2,
    DELETED: 3,
    INVITED: 4
  },
  STATE_MAP: {
    1: 'LOCK',
    2: 'CHECKIN',
    3: 'CHECKOUT',
    4: 'OBSOLETE' // in be  state 4 represents Obsolete in BE
  },
  MATERIAL_STATUS_MAP: {
    1: 'ACTIVE',
    2: 'OBSELETE',
    3: 'HOLD',
    4: 'LOCKED'
  },
  TASK_TYPES: {
    1: 'task',
    2: 'project',
    3: 'milestone'
  },
  DOC_STATE: {
    1: 'LOCK',
    2: 'CHECKIN',
    3: 'CHECKOUT',
    4: 'OBSOLETE'
  },
  DOC_STATE_MAP: {
    LOCK: 1,
    CHECKIN: 2,
    CHECKOUT: 3,
    OBSOLETE: 4
  },
  BOM_STATE_MAP: {
    LOCK: 1,
    CHECKIN: 2,
    CHECKOUT: 3,
    OBSOLETE: 4
  },
  MATERIAL_TYPE: {
    1: 'Material',
    2: 'Resource'
  },
  MODULE_ROUTE: {
    FORM: 'form',
    PROJECTS: 'PROJECTS',
    DOCUMENTS: 'document-view',
    ITEM_MASTER: 'materialmaster',
    BOM: 'bom',
    PROJECT_PLANNER: 'project-planner',
    TIME_SHEET: 'timesheet',
    FORECAST: 'trendsandforecast',
    SETTINGS: 'settings',
    INSIGHTS: 'insights'
  },
  TAG_TYPE_MAP: {
    MATERIAL: 1,
    TASK: 2,
    FORM: 3
  },
  FORM_STATE: {
    1: 'OPEN',
    2: 'DRAFT',
    3: 'REOPENED',
    4: 'CLOSED',
    5: 'CANCELLED'
  },
  FORM_STATE_MAP: {
    OPEN: 1,
    DRAFT: 2,
    REOPENED: 3,
    CLOSED: 4,
    CANCELLED: 5
  },
  FORM_TAB_STATUS: {
    WORKFLOW: 1,
    COMMENTS: 2,
    REVISION_HISTORY: 3,
    HISTORY: 4,
    LINKED_ENTITIES: 5
  },
  BEACON_DASHBOARD: {
    TEMPLATE_NAME: 'BEACONsales',
    TARGET_TEMPLATE_NAME: 'BEACONTarget',
    STAGING_TENANT_ID: 'f56a5367-218e-4b70-bc74-2aed5085c693'
  },
  STANDARD_MATERIAL_FORM: {
    name: 'Standard Material Form',
    form_type: 11 // this is the default form type id for standard material form
  },
  STANDARD_BOM_FORM: {
    name: 'Standard BOM Form',
    form_type: 12 // this is the default form type id for standard bom form
  },
  DOCUMENT_REVISIONING_FORM: {
    name: 'Document Revisioning Form',
    form_type: 14
  },
  ANNOTATION_TYPE: {
    4: 'path'
  },
  COLORS: [
    '#FF5733', '#33FF57', '#3357FF', '#FF33A1', '#A1FF33', '#5733FF', '#33FFF5', '#FF5733', '#F533FF', '#33FF57',
    '#57FF33', '#FF3357', '#33A1FF', '#33FF7F', '#7F33FF', '#FF7F33', '#33FFAF', '#AF33FF', '#FF33AF', '#AFF533',
    '#FF5733', '#5733FF', '#33FF7F', '#A1FF33', '#FF33F5', '#FF5733', '#33A1FF', '#F533FF', '#FF3357', '#7F33FF',
    '#FF7F33', '#AF33FF', '#33FFAF', '#AFF533', '#33FF57', '#5733FF', '#57FF33', '#FF3357', '#33A1FF', '#33FF7F',
    '#7F33FF', '#FF33F5', '#FF7F33', '#33FFAF', '#AFF533', '#FF5733', '#5733FF', '#A1FF33', '#F533FF', '#FF3357',
    '#33FF57', '#FF5733', '#33A1FF', '#7F33FF', '#33FF7F', '#FF33AF', '#33FFAF', '#AFF533', '#FF5733', '#5733FF',
    '#57FF33', '#FF3357', '#33A1FF', '#FF7F33', '#AF33FF', '#FF33F5', '#33FF57', '#FF5733', '#F533FF', '#33FF7F',
    '#7F33FF', '#FF33AF', '#AFF533', '#FF5733', '#33FFAF', '#FF5733', '#5733FF', '#A1FF33', '#FF33F5', '#33FF57',
    '#FF5733', '#33A1FF', '#F533FF', '#FF3357', '#7F33FF', '#FF7F33', '#AF33FF', '#33FFAF', '#AFF533', '#FF5733'
  ],
  CORE_FEATURES: {
    MATERIAL: 1,
    BOM: 2,
    DOCUMENTS: 3,
    TASKS: 4,
    FORMS: 5,
    PRODUCT_CODE: 6,
    USERS: 7
  },
  CORE_FEATURES_INVERTED:
    {
      1: 'MATERIAL',
      2: 'BOM',
      3: 'DOCUMENTS',
      4: 'TASKS',
      5: 'FORMS',
      6: 'PRODUCT_CODE',
      7: 'USERS'
    },
  NODE_TYPE: { PHASE: 1, STEP: 2 },
  WORKFLOW_TYPE: {
    ONLY_STEPS: 'NORMAL',
    STEPS_WITH_PHASES: 'PARTIAL',
    FULL: 'FULL'
  },
  FORM_TYPE: {
    TEXT: 1,
    LONG_TEXT: 2,
    NUMBER: 3,
    CONFIGRATION_LIST: 4,
    COMPANY: 5,
    ATTACHMENT: 6,
    DATE: 7,
    TIME: 8,
    USER: 9,
    MULTI_USER: 10,
    MULTI_COMPANY: 11,
    BOOLEAN: 12,
    LOCATION: 13,
    MATERIALS: 14,
    PRODUCT_CODE: 15
  },
  elementColor: {
    green: '108, 225, 140',
    orange: '250, 160, 57',
    blue: '109, 132, 245',
    purple: '223, 119, 237',
    red: '252, 86, 88'
  }
}
