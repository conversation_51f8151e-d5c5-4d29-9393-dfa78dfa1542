<template>
  <div class="stepper-header">
    <div
      v-for="(label, index) in steps"
      :key="index"
      class="step-item"
    >
      <div
        :class="['step-label', { active: index <= currentStepIndex }]"
      >
        {{ label }}
      </div>

      <!-- Divider line except after the last step -->
      <div
        v-if="index < steps.length - 1"
        :class="['divider', { active: index < currentStepIndex }]"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    step: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      steps: ['Basic Info', 'Customise Fields', 'Completed']
    }
  },
  computed: {
    currentStepIndex () {
      return this.steps.indexOf(this.step)
    }
  }
}
</script>

<style scoped>
.stepper-header {
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-item {
  display: flex;
  align-items: center;
}

.step-label {
  padding: 0 10px;
  font-size: 13px;
  color: #999;
  font-weight: 500;
  white-space: nowrap;
}

.step-label.active {
  color: var(--brand-color);
  font-weight: 600;
}

.divider {
  height: 2px;
  width: 40px;
  background-color: #ddd;
}

.divider.active {
  background-color: var(--brand-color);
}
</style>
