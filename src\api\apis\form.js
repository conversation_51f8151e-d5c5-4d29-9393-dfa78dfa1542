import { runMutation, runQuery } from '../graphQl'
import * as formQuery from '../query/form'
import http from '../http'
import config from '../../config.js'
import store from '../../store'

export const getFormTypeList = async () => {
  return runQuery(formQuery.GetAllFormTypeListQuery(), {}, 'tenant')
}

export const getFormCustomFeilds = (templateId, userId, value = 'forms', collaborator = false) => {
  const conditions = {}
  conditions.visibility = { _eq: true }
  if (value === 'forms') {
    conditions.template_version = { active: { _eq: true }, template_id: { _in: templateId } }
  } else if (value === 'insights') {
    conditions.template_version = { active: { _eq: true }, core_forms: { forms_user_lists: { user_id: { _eq: userId } } } }
  }
  return runQuery(formQuery.GetFormCustomFieldsQuery(), { conditions }, 'tenant')
}

export const getFormFieldValue = (fieldId) => {
  return runQuery(formQuery.GetFormFieldValueQuery(), { field_id: fieldId }, 'tenant')
}

export const getFormFields = async () => {
  return runQuery(formQuery.GetAllFormFieldsQuery(), {}, 'tenant')
}

export const createForm = async (data, formType, name, WFTId, sequenceTemplateId) => {
  return runQuery(formQuery.CreateFormQuery(), { data, form_type: formType, name, wftId: WFTId, sequence_template_id: sequenceTemplateId }, 'tenant')
}

export const updateForm = async (templateId, templateFields, name, WFTId, linkedFormSeqenceId) => {
  return runMutation(formQuery.UpdateFormMutation(), { templateId, templateFields, name, wftId: WFTId, sequence_template_id: linkedFormSeqenceId }, 'tenant')
}

export const getFormTemplates = async (settings = false) => {
  const conditions = { }
  if (!settings) {
    conditions.form_type = { _nin: [config.STANDARD_MATERIAL_FORM.form_type, config.STANDARD_BOM_FORM.form_type] }
  }
  if (store.getters?.collaborator) {
    const projectId = store.getters.projectIdForCollaborator
    const collaboratorId = store.getters?.collaboratorId
    conditions.template_versions = { core_forms: { tenant_id: { _eq: collaboratorId } } }
    if (!projectId) {
      conditions.template_versions.core_forms.project_id = { _is_null: true }
    } else {
      conditions.template_versions.core_forms.project_id = { _eq: store.getters.projectIdForCollaborator }
    }
  }
  return runQuery(formQuery.GetAllFormTemplatesQuery(), { conditions }, 'tenant')
}
export const getFormTemplateswithPagination = async (skip, perPage, settings = false, search, sorting) => {
  const conditions = {}
  let orderBy = { created_on: 'desc' }
  if (sorting === '2') {
    orderBy = { created_on: 'asc' }
  } else if (sorting === '3') {
    orderBy = { name: 'asc' }
  } else if (sorting === '4') {
    orderBy = { name: 'desc' }
  } else orderBy = { created_on: 'desc' }
  if (!settings) {
    conditions.form_type = { _nin: [config.STANDARD_MATERIAL_FORM.form_type, config.STANDARD_BOM_FORM.form_type] }
  }
  if (search?.length) {
    conditions.name = { _ilike: `%${search}%` }
  }
  if (store.getters?.collaborator) {
    const projectId = store.getters.projectIdForCollaborator
    const collaboratorId = store.getters?.collaboratorId
    conditions.template_versions = { core_forms: { tenant_id: { _eq: collaboratorId } } }
    if (!projectId) {
      conditions.template_versions.core_forms.project_id = { _is_null: true }
    } else {
      conditions.template_versions.core_forms.project_id = { _eq: store.getters.projectIdForCollaborator }
    }
  }
  return runQuery(formQuery.GetAllFormTemplatesWithPaginationQuery(), { conditions, perPage, skip, orderBy }, 'tenant')
}

export const formTemplateNameCheck = (name) => {
  return runQuery(formQuery.formTemplateNameCheckQuery(), { name }, 'tenant')
}

export const getDetailFormTemplate = async (id) => {
  let isCollaborator = false
  if (store.getters?.collaborator) {
    isCollaborator = true
  }
  return runQuery(formQuery.GetDetailFormTemplateQuery(), { id, isCollaborator }, 'tenant')
}

export const SaveFormData = async (body, isProject) => {
  return http.POST(config.serverEndpoint + '/forms', body, isProject ? 'project' : 'tenant')
}

// make sure any new condtions added to GetFormsByFormTemplateId  functions should be add into GetAllFormsDataByFormTemplateId
export const GetFormsByFormTemplateId = async (formTemplateVersionId, isProject, filter, isAdmin, userId, fieldId, customFilters = [], formFieldsTypeMap, sorting) => {
  const collaborator = store.getters?.collaborator
  const conditions = {
    _and: []
  }
  let orderBy = { created_on: 'desc' }
  if (sorting === '2') {
    orderBy = { created_on: 'asc' }
  } else orderBy = { created_on: 'desc' }
  conditions._and.push({
    template_version: { template_id: { _eq: formTemplateVersionId } },
    next_revisions_aggregate: { count: { arguments: ['id'], predicate: { _eq: 0 } } }
  })
  if (customFilters.length) {
    customFilters.forEach((customFilter) => {
      if (customFilter.value !== '') {
        if (formFieldsTypeMap[customFilter.field] !== config.FORM_TYPE.CONFIGRATION_LIST) {
          conditions._and.push({
            forms_metadata_by_id: {
              string_value: { _ilike: '%' + customFilter.value + '%' },
              field_id: { _eq: customFilter.field }
            }
          })
        } else {
          conditions._and.push({
            forms_config_lists: {
              custom_list_value_id: { _in: customFilter.value },
              field_id: { _eq: customFilter.field }
            }
          })
        }
      }
    })
  }
  const openFormsConditions = { ...conditions }; const draftFormsConditions = { ...conditions }; const reopenedFormsConditions = { ...conditions }; const closedFormsConditions = { ...conditions }
  openFormsConditions.status = { _eq: 1 }
  draftFormsConditions.status = { _eq: 2 }
  reopenedFormsConditions.status = { _eq: 3 }
  closedFormsConditions.status = { _eq: 4 }
  if (filter.createdOnDate) {
    const createdDate = new Date(filter.createdOnDate.to)
    createdDate.setDate(createdDate.getDate() + 1)
    if (filter.createdOnDate.from && filter.createdOnDate.to) {
      conditions._and.push({
        created_on: {
          _gte: new Date(filter.createdOnDate.from),
          _lt: createdDate.toISOString()
        }
      })
    } else if (filter.createdOnDate.from) {
      conditions._and.push({
        created_on: { _gte: new Date(filter.createdOnDate.from) }
      })
    } else if (filter.createdOnDate.to) {
      conditions._and.push({
        created_on: { _lt: createdDate.toISOString() }
      })
    }
  }
  if (filter.updatedOnDate) {
    const updatedDate = new Date(filter.updatedOnDate.to)
    updatedDate.setDate(updatedDate.getDate() + 1)
    if (filter.updatedOnDate.from && filter.updatedOnDate.to) {
      conditions._and.push({
        updated_on: {
          _gte: new Date(filter.updatedOnDate.from),
          _lt: updatedDate.toISOString()
        }
      })
    } else if (filter.updatedOnDate.from) {
      conditions._and.push({
        updated_on: { _gte: new Date(filter.updatedOnDate.from) }
      })
    } else if (filter.updatedOnDate.to) {
      conditions._and.push({
        updated_on: { _lt: updatedDate.toISOString() }
      })
    }
  }
  if (filter.dueDate) {
    const dueDate = new Date(filter.dueDate.to)
    dueDate.setDate(dueDate.getDate() + 1)
    if (filter.dueDate.from && filter.dueDate.to) {
      conditions._and.push({
        due_date: {
          _gte: new Date(filter.dueDate.from),
          _lt: dueDate.toISOString()
        }
      })
    } else if (filter.dueDate.from) {
      conditions._and.push({
        due_date: { _gte: new Date(filter.dueDate.from) }
      })
    } else if (filter.dueDate.to) {
      conditions._and.push({
        due_date: { _lt: dueDate.toISOString() }
      })
    }
  }
  if (filter.searchById) {
    conditions._and.push({
      id: { _eq: filter.searchById }
    })
  }
  if (filter.assigneeId) {
    conditions._and.push({
      forms_user_list: { user_id: { _in: filter.assigneeId } }
    })
  }
  if (filter.status !== -1 && filter.status) {
    conditions._and.push({
      status: { _eq: filter.status }
    })
  }
  if (isProject !== true && !collaborator) {
    conditions._and.push({
      project_id: { _is_null: true }
    })
  }
  if (collaborator) {
    const projectIdForCollaborator = store.getters.projectIdForCollaborator
    const collaboratorId = store.getters?.collaboratorId
    conditions._and.push({
      tenant_id: { _eq: collaboratorId }
    })
    if (projectIdForCollaborator) {
      conditions._and.push({
        project_id: { _eq: projectIdForCollaborator }
      })
    } else {
      conditions._and.push({
        project_id: { _is_null: true }
      })
    }
  }
  if (!isAdmin) {
    conditions._and.push({
      _or: [{ status: { _neq: 2 } }, { created_by: { _eq: userId } }]
    })
  }
  return runQuery(
    formQuery.GetFormsByFormTemplateIdQuery(),
    {
      conditions,
      openFormsConditions,
      draftFormsConditions,
      reopenedFormsConditions,
      closedFormsConditions,
      orderBy,
      limit: filter.perPage,
      offset: filter.jump,
      field_id: fieldId
    },
    isProject ? 'project' : 'tenant'
  )
}

export const GetAllFormsDataByFormTemplateId = async (formTemplateVersionId, isProject, filter, isAdmin, userId, fieldId, customFilters = [], formFieldsTypeMap) => {
  const collaborator = store.getters?.collaborator
  const conditions = {
    _and: [
    ]
  }
  conditions._and.push({
    template_version: { template_id: { _eq: formTemplateVersionId } },
    next_revisions_aggregate: { count: { arguments: ['id'], predicate: { _eq: 0 } } }
  })
  if (customFilters.length) {
    const metadataFilters = {}
    const formsConfigLists = {}
    metadataFilters._or = []
    formsConfigLists._or = []
    customFilters.forEach((customFilter) => {
      if (formFieldsTypeMap[customFilter.field] !== 4) {
        metadataFilters._or.push(
          {
            string_value: { _ilike: '%' + customFilter.value + '%' },
            field_id: { _eq: customFilter.field }
          }
        )
      } else {
        formsConfigLists._or.push(
          {
            custom_list_value_id: { _in: customFilter.value },
            field_id: { _eq: customFilter.field }
          }
        )
      }
    })
    if (formsConfigLists._or.length) {
      conditions._and.push({ forms_config_lists: formsConfigLists })
    }
    if (metadataFilters._or.length) {
      conditions._and.push({ forms_metadata_by_id: metadataFilters })
    }
  }
  if (filter.createdOnDate) {
    const createdDate = new Date(filter.createdOnDate.to)
    createdDate.setDate(createdDate.getDate() + 1)
    if (filter.createdOnDate.from && filter.createdOnDate.to) {
      conditions._and.push({
        created_on: { _gte: new Date(filter.createdOnDate.from), _lt: createdDate.toISOString() }
      })
    } else if (filter.createdOnDate.from) {
      conditions._and.push({
        created_on: { _gte: new Date(filter.createdOnDate.from) }
      })
    } else if (filter.createdOnDate.to) {
      conditions._and.push({
        created_on: { _lt: createdDate.toISOString() }
      })
    }
  }
  if (filter.dueDate) {
    const dueDate = new Date(filter.dueDate.to)
    dueDate.setDate(dueDate.getDate() + 1)
    if (filter.dueDate.from && filter.dueDate.to) {
      conditions._and.push({
        due_date: { _gte: new Date(filter.dueDate.from), _lt: dueDate.toISOString() }
      })
    } else if (filter.dueDate.from) {
      conditions._and.push({
        due_date: { _gte: new Date(filter.dueDate.from) }
      })
    } else if (filter.dueDate.to) {
      conditions._and.push({
        due_date: { _lt: dueDate.toISOString() }
      })
    }
  }
  if (filter.searchById) {
    conditions._and.push({
      id: { _eq: filter.searchById }
    })
  }
  if (filter.assigneeId) {
    conditions._and.push({
      forms_user_list: { user_id: { _in: filter.assigneeId } }
    })
  }
  if (filter.status !== -1 && filter.status) {
    conditions._and.push({
      status: { _eq: filter.status }
    })
  }
  if (isProject !== true && !collaborator) {
    conditions._and.push({
      project_id: { _is_null: true }
    })
  }
  if (collaborator) {
    const projectIdForCollaborator = store.getters.projectIdForCollaborator
    const collaboratorId = store.getters?.collaboratorId
    conditions._and.push({
      tenant_id: { _eq: collaboratorId }
    })
    if (projectIdForCollaborator) {
      conditions._and.push({
        project_id: { _eq: projectIdForCollaborator }
      })
    } else {
      conditions._and.push({
        project_id: { _is_null: true }
      })
    }
  }
  if (!isAdmin) {
    conditions._and.push({
      _or: [{ status: { _neq: 2 } }, { created_by: { _eq: userId } }]
    })
  }
  return runQuery(formQuery.GetAllFormsDataByFormTemplateQuery(),
    { conditions, limit: filter.perPage, offset: filter.jump }, isProject ? 'project' : 'tenant')
}

export const GetAllformFieldsOfAllVersionsByTemplateId = (templateId, isProject) => {
  return runQuery(formQuery.GetAllformFieldsOfAllVersionsByTemplateIdQuery(), { templateId }, isProject ? 'project' : 'tenant')
}
export const GetAllformFieldsOfAllVersionsByFormTypeId = (formType, isProject) => {
  return runQuery(formQuery.GetAllformFieldsOfAllVersionsByFormTypeIdQuery(), { formType }, isProject ? 'project' : 'tenant')
}
export const GetFormDataByFormId = (formId, isProject) => {
  return runQuery(formQuery.GetFormDataByFormIdQuery(), { id: formId }, isProject ? 'project' : 'tenant')
}

// Form Widget Data
export const getUserList = async (isProject) => {
  return runQuery(formQuery.GetUserListQuery(), {}, isProject ? 'project' : 'tenant')
}
export const GetUserListQuerywithInvitedStatus = async (isProject) => {
  return runQuery(formQuery.GetUserListQuerywithInvited(), {}, isProject ? 'project' : 'tenant')
}

export const GetMaterialListByIdsQuery = async (ids = []) => {
  return runQuery(formQuery.GetMaterialListByIdsQuery(), { ids }, 'tenant')
}

export const getMaterialForFormList = async (searchKeyWord = '') => {
  return runQuery(formQuery.GetMaterialListQuery(), { searchKeyWord: `%${searchKeyWord}%` }, 'tenant')
}

export const EditForm = async (payload) => {
  return http.POST(config.serverEndpoint + '/forms/update', payload, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getHistory = async (formId) => {
  return http.GET(config.serverEndpoint + `/forms/history/${formId}`, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getWfHistory = async (instanceId) => {
  return http.GET(config.serverEndpoint + `/workflows/wfhistory/${instanceId}`, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
export const getFormIdByformType = async (formTypeId) => {
  return runQuery(formQuery.getFormIdByformTypeQuery(), { form_type: formTypeId }, 'tenant')
}

export const DeleteForm = async (id, isProject) => {
  return runMutation(formQuery.deleteFormMutation(), { id }, isProject ? 'project' : 'tenant')
}

export const getTenantUserList = async (id, isProject) => {
  return runQuery(formQuery.GetTenantUsersList(), { id }, isProject ? 'project' : 'tenant')
}
// this query is used only in forms and where viewers has disabled
export const getTenantUsersListwithInvitedUsers = async (id, isProject) => {
  return runQuery(formQuery.GetTenantUsersListwithInvitedUsers(), { id }, isProject ? 'project' : 'tenant')
}
// this query is used only in forms and where viewers has disabled
export const GetActiveTenantUsersList = async (id, isProject) => {
  return runQuery(formQuery.GetActiveTenantUsersListQuery(), { id }, isProject ? 'project' : 'tenant')
}

export const GetAllAssigneesOfATemplate = async (templateId, isProject, projectId = null) => {
  return runQuery(formQuery.getAllAssigneesOfATemplateQuery(projectId), { templateId }, isProject ? 'project' : 'tenant')
}

export const GetFieldsForCustomFormFilters = async (templateId, isProject) => {
  return runQuery(formQuery.getFieldsForCustomFormFiltersQuery(), { templateId }, isProject ? 'project' : 'tenant')
}
export const reAssignToNewUser = async (targetTenantId, templateId, oldUserId, newUserId, status, isProject) => {
  return runQuery(formQuery.reAssignToNewUserMutation(), { targetTenantId, templateId, oldUserId, newUserId, status }, isProject ? 'project' : 'tenant')
}
export const GetStandardBomFormTemplate = async (tempId, stdBomVersionId = null) => {
  const conditions = { id: { _eq: tempId } }
  if (store.getters?.collaborator) {
    conditions.tenant_id = { _eq: store.getters?.collaboratorId }
  }
  // if standard bom version id is provided then we will get the template with that version id
  // otherwise we will get the active template
  let conditions2 = {}
  if (stdBomVersionId) {
    conditions2 = { id: { _eq: stdBomVersionId } }
  } else {
    conditions2 = { active: { _eq: true } }
  }

  return runQuery(formQuery.GetStandardBomFormTemplateQuery(), { conditions, conditions2 }, 'tenant')
}
export const formWorkFlowLinkingApi = async (linkingData) => {
  return runMutation(formQuery.formWorkFlowLinkingMutation(), { updates: linkingData }, 'tenant')
}
export const getFullformTemplates = async (linkingData) => {
  return runQuery(formQuery.getFullformTemplatesQuery(), { updates: linkingData }, 'tenant')
}
export const reOpenForms = async (body, isProject) => {
  return runMutation(formQuery.reOpenForm(), { cost_impact: body.cost_impact, schedule_impact: body.schedule_impact, due_date: body.due_date, form_id: body.form_id, input_payload: body.input_payload, step_id: body.step_id, workflowStepAssignees: body.workflowStepAssignees }, isProject ? 'project' : 'tenant')
}
export const getFormRevision = async (id) => {
  return runQuery(formQuery.getFormRevision(), { id }, 'tenant')
}

export const getFormLinks = async (formId) => {
  return http.GET(config.serverEndpoint + `/forms/formLinks/${formId}`, store.getters?.isOnProjectLevel ? 'project' : 'tenant')
}
